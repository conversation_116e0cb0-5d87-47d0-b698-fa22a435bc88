# Roti Ragil Invoice App

Aplikasi invoice untuk Roti Ragil yang dibuat dengan React dan Vite. Aplikasi ini dapat dijalankan di browser dan di-install sebagai Progressive Web App (PWA) di perangkat Android.

## Fitur

- ✅ **Dashboard** - Overview data invoice, produk, dan pelanggan
- ✅ **Manajemen Produk** - <PERSON><PERSON>, edit, hapus produk
- ✅ **Manajemen Pelanggan** - <PERSON><PERSON>, edit, hapus pelanggan
- ✅ **Buat Invoice** - Form pembuatan invoice dengan pilihan produk dan pelanggan
- ✅ **Daftar Invoice** - List semua invoice dengan status
- ✅ **Export PDF** - Download invoice dalam format PDF
- ✅ **PWA Support** - Dapat di-install di Android sebagai aplikasi

## Tech Stack

- **React 18** - Frontend framework
- **Vite** - Build tool dan development server
- **React Router** - Navigation
- **Tailwind CSS** - Styling
- **Lucide React** - Icons
- **jsPDF** - PDF generation
- **LocalStorage** - Data persistence

## Instalasi dan Menjalankan

1. **Install dependencies:**
   ```bash
   npm install
   ```

2. **Jalankan development server:**
   ```bash
   npm run dev
   ```

3. **Buka browser dan akses:**
   ```
   http://localhost:3000
   ```

4. **Build untuk production:**
   ```bash
   npm run build
   ```

## Cara Menggunakan

### 1. Dashboard
- Melihat overview total produk, pelanggan, invoice, dan pendapatan
- Melihat daftar invoice terbaru

### 2. Manajemen Produk
- Klik "Produk" di sidebar
- Tambah produk baru dengan nama, harga, dan deskripsi
- Edit atau hapus produk yang sudah ada

### 3. Manajemen Pelanggan
- Klik "Pelanggan" di sidebar
- Tambah pelanggan baru dengan nama, alamat, telepon, dan email
- Edit atau hapus pelanggan yang sudah ada

### 4. Membuat Invoice
- Klik "Invoice" di sidebar, lalu "Buat Invoice"
- Pilih pelanggan dan tanggal
- Tambah item dengan memilih produk dan quantity
- Sistem akan otomatis menghitung total
- Simpan invoice

### 5. Mengelola Invoice
- Lihat daftar semua invoice
- Ubah status invoice (Draft, Terkirim, Lunas)
- Lihat detail invoice
- Download invoice sebagai PDF

## Install sebagai PWA di Android

1. Buka aplikasi di browser Chrome Android
2. Tap menu (3 titik) di browser
3. Pilih "Add to Home screen" atau "Install app"
4. Aplikasi akan ter-install dan bisa diakses seperti aplikasi native

## Data Storage

Aplikasi menggunakan LocalStorage browser untuk menyimpan data:
- Data akan tersimpan di browser lokal
- Data tidak akan hilang saat browser ditutup
- Data bersifat lokal per browser/device

## Struktur Project

```
src/
├── components/          # Komponen reusable
│   └── Layout.jsx      # Layout utama dengan sidebar
├── pages/              # Halaman-halaman utama
│   ├── Dashboard.jsx   # Halaman dashboard
│   ├── Products.jsx    # Manajemen produk
│   ├── Customers.jsx   # Manajemen pelanggan
│   ├── Invoices.jsx    # Daftar invoice
│   ├── CreateInvoice.jsx # Form buat invoice
│   └── InvoiceDetail.jsx # Detail invoice
├── services/           # Service layer
│   ├── storage.js      # LocalStorage operations
│   └── pdfService.js   # PDF generation
├── App.jsx            # Root component
├── main.jsx           # Entry point
└── index.css          # Global styles
```

## Browser Support

- Chrome (recommended)
- Firefox
- Safari
- Edge

## Kontribusi

Aplikasi ini dibuat untuk Roti Ragil. Untuk pengembangan lebih lanjut, silakan hubungi developer.

## License

Private - Roti Ragil
