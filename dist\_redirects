# Netlify redirects for SPA routing
# This ensures all routes are handled by React Router

# Handle all routes by serving index.html
/*    /index.html   200

# API routes (if any in the future)
/api/*  /api/:splat  200

# Static assets should be served normally
/assets/*  /assets/:splat  200
/favicon.svg  /favicon.svg  200
/logo.svg  /logo.svg  200
/app-icon.svg  /app-icon.svg  200
/manifest.json  /manifest.json  200
/sw.js  /sw.js  200
