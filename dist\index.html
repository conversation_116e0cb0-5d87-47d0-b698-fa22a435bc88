<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/favicon.svg?v=3.0.0" />
    <link rel="icon" type="image/png" href="/logo.svg?v=3.0.0" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="theme-color" content="#F59E0B" />
    <meta name="description" content="Aplikasi Invoice Roti Ragil - Bread, Cake, and Snack" />
    <link rel="apple-touch-icon" href="/app-icon.svg?v=3.0.0" />
    <link rel="manifest" href="/manifest.json?v=3.0.0" />

    <!-- PWA Meta Tags for Better Icon Caching -->
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="Roti Ragil">

    <!-- Additional icon sizes for better compatibility -->
    <link rel="apple-touch-icon" sizes="180x180" href="/app-icon.svg?v=3.0.0">
    <link rel="icon" type="image/png" sizes="32x32" href="/favicon.svg?v=3.0.0">
    <link rel="icon" type="image/png" sizes="16x16" href="/favicon.svg?v=3.0.0">
    <title>Roti Ragil Invoice</title>
    <script type="module" crossorigin src="/assets/index-752142d2.js"></script>
    <link rel="stylesheet" href="/assets/index-f10ab15f.css">
  </head>
  <body>
    <div id="root"></div>
    

    <!-- Service Worker Registration -->
    <script>
      if ('serviceWorker' in navigator) {
        window.addEventListener('load', () => {
          navigator.serviceWorker.register('/sw.js')
            .then((registration) => {
              console.log('SW registered: ', registration);

              // Check for updates
              registration.addEventListener('updatefound', () => {
                const newWorker = registration.installing;
                newWorker.addEventListener('statechange', () => {
                  if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                    // New content is available, prompt user to refresh
                    if (confirm('Aplikasi telah diperbarui! Refresh untuk melihat perubahan terbaru?')) {
                      window.location.reload();
                    }
                  }
                });
              });
            })
            .catch((registrationError) => {
              console.log('SW registration failed: ', registrationError);
            });
        });
      }

      // Function to manually refresh icon cache
      window.refreshIconCache = function() {
        if ('serviceWorker' in navigator && navigator.serviceWorker.controller) {
          const messageChannel = new MessageChannel();
          messageChannel.port1.onmessage = function(event) {
            if (event.data.type === 'ICONS_CACHE_CLEARED') {
              console.log('Icon cache cleared, reloading...');
              window.location.reload();
            }
          };

          navigator.serviceWorker.controller.postMessage({
            type: 'REFRESH_ICONS'
          }, [messageChannel.port2]);
        }
      };
    </script>
  </body>
</html>
