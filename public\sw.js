// Service Worker for Roti Ragil Invoice App
const CACHE_NAME = 'roti-ragil-v2.0.0';
const ICON_CACHE_NAME = 'roti-ragil-icons-v2.0.0';

// Files to cache
const urlsToCache = [
  '/',
  '/manifest.json?v=2.0.0',
  '/favicon.svg?v=2.0.0',
  '/logo.svg?v=2.0.0',
  '/app-icon.svg?v=2.0.0'
];

// Install event - cache resources
self.addEventListener('install', (event) => {
  console.log('Service Worker: Installing...');
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then((cache) => {
        console.log('Service Worker: Caching files');
        return cache.addAll(urlsToCache);
      })
      .then(() => {
        console.log('Service Worker: Installed successfully');
        // Force activation of new service worker
        return self.skipWaiting();
      })
  );
});

// Activate event - clean up old caches
self.addEventListener('activate', (event) => {
  console.log('Service Worker: Activating...');
  event.waitUntil(
    caches.keys().then((cacheNames) => {
      return Promise.all(
        cacheNames.map((cacheName) => {
          // Delete old caches
          if (cacheName !== CACHE_NAME && cacheName !== ICON_CACHE_NAME) {
            console.log('Service Worker: Deleting old cache:', cacheName);
            return caches.delete(cacheName);
          }
        })
      );
    }).then(() => {
      console.log('Service Worker: Activated successfully');
      // Take control of all pages immediately
      return self.clients.claim();
    })
  );
});

// Fetch event - serve from cache or network
self.addEventListener('fetch', (event) => {
  const { request } = event;
  const url = new URL(request.url);

  // Handle navigation requests (for SPA routing)
  if (request.mode === 'navigate') {
    event.respondWith(
      fetch(request)
        .catch(() => {
          // If network fails, serve index.html for SPA routing
          return caches.match('/index.html') || caches.match('/');
        })
    );
    return;
  }

  // Handle icon requests specially
  if (url.pathname.includes('.svg') || url.pathname.includes('manifest.json')) {
    event.respondWith(
      fetch(request)
        .then((response) => {
          // Clone the response
          const responseClone = response.clone();

          // Open the cache
          caches.open(ICON_CACHE_NAME)
            .then((cache) => {
              // Add the response to cache
              cache.put(request, responseClone);
            });

          return response;
        })
        .catch(() => {
          // If network fails, try cache
          return caches.match(request);
        })
    );
  } else {
    // Handle other requests normally
    event.respondWith(
      caches.match(request)
        .then((response) => {
          // Return cached version or fetch from network
          return response || fetch(request);
        })
    );
  }
});

// Message event - handle manual cache refresh
self.addEventListener('message', (event) => {
  if (event.data && event.data.type === 'REFRESH_ICONS') {
    console.log('Service Worker: Refreshing icon cache...');
    
    // Clear icon cache
    caches.delete(ICON_CACHE_NAME).then(() => {
      console.log('Service Worker: Icon cache cleared');
      
      // Notify client that cache is cleared
      event.ports[0].postMessage({
        type: 'ICONS_CACHE_CLEARED'
      });
    });
  }
});

// Handle updates
self.addEventListener('message', (event) => {
  if (event.data && event.data.type === 'SKIP_WAITING') {
    self.skipWaiting();
  }
});
