import React, { useEffect, useState } from 'react'
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom'
import Layout from './components/Layout'
import Dashboard from './pages/Dashboard'
import Products from './pages/Products'
import Customers from './pages/Customers'
import Invoices from './pages/Invoices'
import CreateInvoice from './pages/CreateInvoice'
import InvoiceDetail from './pages/InvoiceDetail'
import DataSyncNotification from './components/DataSyncNotification'
import { autoSyncDefaultData } from './services/storage'

function App() {
  const [syncResult, setSyncResult] = useState(null)

  useEffect(() => {
    // Auto-sync default data when app starts
    const performSync = async () => {
      try {
        console.log('Starting auto-sync...')
        const result = await autoSyncDefaultData()
        console.log('Sync result:', result)
        setSyncResult(result)
      } catch (error) {
        console.error('Error during auto-sync:', error)
        setSyncResult({
          updated: false,
          error: true,
          message: '<PERSON><PERSON><PERSON><PERSON> k<PERSON><PERSON> saat sinkronisasi data'
        })
      }
    }

    performSync()
  }, [])

  const handleDismissNotification = () => {
    setSyncResult(null)
  }

  return (
    <Router>
      <DataSyncNotification
        syncResult={syncResult}
        onDismiss={handleDismissNotification}
      />
      <Layout>
        <Routes>
          <Route path="/" element={<Dashboard />} />
          <Route path="/products" element={<Products />} />
          <Route path="/customers" element={<Customers />} />
          <Route path="/invoices" element={<Invoices />} />
          <Route path="/invoices/create" element={<CreateInvoice />} />
          <Route path="/invoices/:id" element={<InvoiceDetail />} />
        </Routes>
      </Layout>
    </Router>
  )
}

export default App
