import React from 'react'
import { <PERSON> } from 'react-router-dom'
import { PlusCircle, FileText, Zap } from 'lucide-react'
import {
  ModernPlusIcon,
  ModernDocumentIcon,
  ModernZapIcon,
  ModernCreateIcon,
  ModernInvoiceIcon
} from './ModernIcons'

const CreateInvoiceButton = ({ 
  variant = 'primary', 
  size = 'default', 
  className = '',
  showIcon = true,
  iconType = 'plus'
}) => {
  const getIcon = () => {
    const iconSize = size === 'small' ? 'h-3.5 w-3.5' : size === 'large' ? 'h-5 w-5' : 'h-4 w-4'

    switch (iconType) {
      case 'plus':
        return <ModernPlusIcon className={iconSize} />
      case 'file':
        return <ModernDocumentIcon className={iconSize} />
      case 'zap':
        return <ModernZapIcon className={iconSize} />
      case 'create':
        return <ModernCreateIcon className={iconSize} />
      case 'invoice':
        return <ModernInvoiceIcon className={iconSize} />
      default:
        return <ModernPlusIcon className={iconSize} />
    }
  }

  const getVariantClasses = () => {
    switch (variant) {
      case 'primary':
        return 'bg-gradient-to-r from-amber-500 to-yellow-400 hover:from-amber-600 hover:to-yellow-500 text-white shadow-md hover:shadow-lg'
      case 'secondary':
        return 'bg-white border border-amber-300 text-amber-700 hover:bg-amber-50 hover:border-amber-400'
      case 'minimal':
        return 'bg-amber-50 text-amber-700 hover:bg-amber-100 border border-amber-200'
      default:
        return 'bg-gradient-to-r from-amber-500 to-yellow-400 hover:from-amber-600 hover:to-yellow-500 text-white shadow-md hover:shadow-lg'
    }
  }

  const getSizeClasses = () => {
    switch (size) {
      case 'small':
        return 'px-3 py-1.5 text-xs font-medium'
      case 'default':
        return 'px-4 py-2 text-sm font-medium'
      case 'large':
        return 'px-6 py-3 text-base font-semibold'
      default:
        return 'px-4 py-2 text-sm font-medium'
    }
  }

  const baseClasses = `
    inline-flex items-center justify-center
    rounded-lg
    transition-all duration-200
    transform hover:scale-105 active:scale-95
    focus:outline-none focus:ring-2 focus:ring-amber-500 focus:ring-offset-2
    disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none
    relative overflow-hidden
    group
  `

  const combinedClasses = `
    ${baseClasses}
    ${getVariantClasses()}
    ${getSizeClasses()}
    ${className}
  `.replace(/\s+/g, ' ').trim()

  return (
    <Link
      to="/invoices/create"
      className={combinedClasses}
    >
      {/* Subtle background animation */}
      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-700"></div>

      {showIcon && (
        <span className="mr-2 relative z-10 transition-transform duration-200 group-hover:scale-110">
          {getIcon()}
        </span>
      )}
      <span className="relative z-10 tracking-wide">Buat Invoice</span>
    </Link>
  )
}

export default CreateInvoiceButton
