import React, { useState, useEffect } from 'react'
import { CheckCircle, Package, Users, X, RefreshCw } from 'lucide-react'

const DataSyncNotification = ({ syncResult, onDismiss }) => {
  const [isVisible, setIsVisible] = useState(false)

  useEffect(() => {
    if (syncResult && (syncResult.updated || syncResult.isFirstTime)) {
      setIsVisible(true)
      
      // Auto dismiss after 10 seconds if not manually dismissed
      const timer = setTimeout(() => {
        handleDismiss()
      }, 10000)
      
      return () => clearTimeout(timer)
    }
  }, [syncResult])

  const handleDismiss = () => {
    setIsVisible(false)
    if (onDismiss) {
      onDismiss()
    }
  }

  if (!isVisible || !syncResult) return null

  const getNotificationContent = () => {
    if (syncResult.isFirstTime) {
      return {
        title: 'Selamat Datang! 🎉',
        message: 'Aplikasi telah diinisialisasi dengan data produk dan pelanggan default.',
        icon: CheckCircle,
        bgColor: 'from-green-500 to-emerald-400',
        textColor: 'text-white'
      }
    }

    if (syncResult.updated) {
      return {
        title: 'Data Diperbarui! ✨',
        message: syncResult.message,
        icon: RefreshCw,
        bgColor: 'from-blue-500 to-cyan-400',
        textColor: 'text-white'
      }
    }

    return null
  }

  const content = getNotificationContent()
  if (!content) return null

  const Icon = content.icon

  return (
    <div className="fixed top-4 left-1/2 transform -translate-x-1/2 z-50 max-w-md w-full mx-4">
      <div className={`bg-gradient-to-r ${content.bgColor} ${content.textColor} rounded-lg shadow-lg p-4 border border-white border-opacity-20`}>
        <div className="flex items-start justify-between">
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0">
              <Icon className="h-6 w-6" />
            </div>
            <div className="flex-1">
              <h3 className="text-sm font-semibold">{content.title}</h3>
              <p className="text-xs mt-1 opacity-90">
                {content.message}
              </p>
              
              {syncResult.updated && (
                <div className="mt-3 flex items-center space-x-4 text-xs opacity-80">
                  {syncResult.updatedProducts > 0 && (
                    <div className="flex items-center space-x-1">
                      <Package className="h-3 w-3" />
                      <span>+{syncResult.updatedProducts} produk</span>
                    </div>
                  )}
                  {syncResult.updatedCustomers > 0 && (
                    <div className="flex items-center space-x-1">
                      <Users className="h-3 w-3" />
                      <span>+{syncResult.updatedCustomers} pelanggan</span>
                    </div>
                  )}
                </div>
              )}
              
              <div className="mt-3">
                <button
                  onClick={handleDismiss}
                  className="text-xs bg-white bg-opacity-20 hover:bg-opacity-30 px-3 py-1 rounded-md transition-colors"
                >
                  Tutup
                </button>
              </div>
            </div>
          </div>
          <button
            onClick={handleDismiss}
            className="flex-shrink-0 ml-2 opacity-70 hover:opacity-100 transition-opacity"
          >
            <X className="h-4 w-4" />
          </button>
        </div>
      </div>
    </div>
  )
}

export default DataSyncNotification
