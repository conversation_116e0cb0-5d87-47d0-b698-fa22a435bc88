import React from 'react'

// Modern Plus Icon with gradient effect
export const ModernPlusIcon = ({ className = "h-4 w-4" }) => (
  <svg className={className} viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <defs>
      <linearGradient id="plusGradient" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" stopColor="#FBBF24" />
        <stop offset="100%" stopColor="#F59E0B" />
      </linearGradient>
    </defs>
    <circle cx="12" cy="12" r="10" fill="url(#plusGradient)" fillOpacity="0.1" stroke="url(#plusGradient)" strokeWidth="1.5"/>
    <path d="M12 8v8M8 12h8" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
  </svg>
)

// Modern Document Icon with sparkle effect
export const ModernDocumentIcon = ({ className = "h-4 w-4" }) => (
  <svg className={className} viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <defs>
      <linearGradient id="docGradient" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" stopColor="#FBBF24" />
        <stop offset="100%" stopColor="#F59E0B" />
      </linearGradient>
    </defs>
    <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z" fill="url(#docGradient)" fillOpacity="0.1" stroke="currentColor" strokeWidth="1.5"/>
    <polyline points="14,2 14,8 20,8" stroke="currentColor" strokeWidth="1.5" fill="none"/>
    <line x1="16" y1="13" x2="8" y2="13" stroke="currentColor" strokeWidth="1.5"/>
    <line x1="16" y1="17" x2="8" y2="17" stroke="currentColor" strokeWidth="1.5"/>
    <polyline points="10,9 9,9 8,9" stroke="currentColor" strokeWidth="1.5"/>
    {/* Sparkle effect */}
    <circle cx="18" cy="6" r="1" fill="currentColor" opacity="0.6"/>
    <circle cx="19" cy="4" r="0.5" fill="currentColor" opacity="0.4"/>
  </svg>
)

// Modern Lightning/Zap Icon with energy effect
export const ModernZapIcon = ({ className = "h-4 w-4" }) => (
  <svg className={className} viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <defs>
      <linearGradient id="zapGradient" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" stopColor="#FBBF24" />
        <stop offset="50%" stopColor="#F59E0B" />
        <stop offset="100%" stopColor="#D97706" />
      </linearGradient>
    </defs>
    <polygon points="13 2 3 14 12 14 11 22 21 10 12 10 13 2" fill="url(#zapGradient)" fillOpacity="0.2" stroke="currentColor" strokeWidth="1.5"/>
    {/* Energy particles */}
    <circle cx="6" cy="8" r="0.5" fill="currentColor" opacity="0.6">
      <animate attributeName="opacity" values="0.6;1;0.6" dur="1.5s" repeatCount="indefinite"/>
    </circle>
    <circle cx="18" cy="16" r="0.5" fill="currentColor" opacity="0.4">
      <animate attributeName="opacity" values="0.4;0.8;0.4" dur="2s" repeatCount="indefinite"/>
    </circle>
  </svg>
)

// Modern Create Icon with creative brush effect
export const ModernCreateIcon = ({ className = "h-4 w-4" }) => (
  <svg className={className} viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <defs>
      <linearGradient id="createGradient" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" stopColor="#FBBF24" />
        <stop offset="100%" stopColor="#F59E0B" />
      </linearGradient>
    </defs>
    <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z" fill="url(#createGradient)" fillOpacity="0.1" stroke="currentColor" strokeWidth="1.5"/>
    <circle cx="12" cy="12" r="3" stroke="currentColor" strokeWidth="1.5" fill="none"/>
    <path d="M12 9v6M9 12h6" stroke="currentColor" strokeWidth="1.5"/>
  </svg>
)

// Modern Invoice Icon with receipt design
export const ModernInvoiceIcon = ({ className = "h-4 w-4" }) => (
  <svg className={className} viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <defs>
      <linearGradient id="invoiceGradient" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" stopColor="#FBBF24" />
        <stop offset="100%" stopColor="#F59E0B" />
      </linearGradient>
    </defs>
    <path d="M4 4h16v14l-2-2-2 2-2-2-2 2-2-2-2 2-2-2-2 2V4z" fill="url(#invoiceGradient)" fillOpacity="0.1" stroke="currentColor" strokeWidth="1.5"/>
    <line x1="8" y1="8" x2="16" y2="8" stroke="currentColor" strokeWidth="1.5"/>
    <line x1="8" y1="12" x2="16" y2="12" stroke="currentColor" strokeWidth="1.5"/>
    <line x1="8" y1="16" x2="12" y2="16" stroke="currentColor" strokeWidth="1.5"/>
    <circle cx="18" cy="6" r="2" fill="url(#invoiceGradient)" fillOpacity="0.3"/>
    <path d="M17 6h2" stroke="currentColor" strokeWidth="1"/>
  </svg>
)

export default {
  ModernPlusIcon,
  ModernDocumentIcon,
  ModernZapIcon,
  ModernCreateIcon,
  ModernInvoiceIcon
}
