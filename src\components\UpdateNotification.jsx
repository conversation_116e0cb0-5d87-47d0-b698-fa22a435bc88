import React, { useState, useEffect } from 'react'
import { X, Refresh<PERSON>w, Smartphone } from 'lucide-react'

const UpdateNotification = () => {
  const [showNotification, setShowNotification] = useState(false)
  const [dismissed, setDismissed] = useState(false)

  useEffect(() => {
    // Check if user has seen the icon update notification
    const hasSeenIconUpdate = localStorage.getItem('hasSeenIconUpdate_v2')
    
    if (!hasSeenIconUpdate && !dismissed) {
      // Show notification after 3 seconds
      const timer = setTimeout(() => {
        setShowNotification(true)
      }, 3000)
      
      return () => clearTimeout(timer)
    }
  }, [dismissed])

  const handleDismiss = () => {
    setShowNotification(false)
    setDismissed(true)
    localStorage.setItem('hasSeenIconUpdate_v2', 'true')
  }

  const handleRefreshIcons = () => {
    if (window.refreshIconCache) {
      window.refreshIconCache()
    } else {
      // Fallback: just reload
      window.location.reload()
    }
  }

  if (!showNotification) return null

  return (
    <div className="fixed top-4 right-4 z-50 max-w-sm">
      <div className="bg-gradient-to-r from-amber-500 to-yellow-400 text-white rounded-lg shadow-lg p-4 border border-amber-300">
        <div className="flex items-start justify-between">
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0">
              <Smartphone className="h-6 w-6 text-white" />
            </div>
            <div className="flex-1">
              <h3 className="text-sm font-semibold">Icon Baru Tersedia! 🌾</h3>
              <p className="text-xs mt-1 text-amber-50">
                Kami telah memperbarui icon aplikasi dengan lambang padi gold yang baru.
              </p>
              <div className="mt-3 flex space-x-2">
                <button
                  onClick={handleRefreshIcons}
                  className="inline-flex items-center px-3 py-1 bg-white text-amber-600 text-xs font-medium rounded-md hover:bg-amber-50 transition-colors"
                >
                  <RefreshCw className="h-3 w-3 mr-1" />
                  Update Icon
                </button>
                <button
                  onClick={handleDismiss}
                  className="text-xs text-amber-100 hover:text-white transition-colors"
                >
                  Nanti saja
                </button>
              </div>
            </div>
          </div>
          <button
            onClick={handleDismiss}
            className="flex-shrink-0 ml-2 text-amber-100 hover:text-white transition-colors"
          >
            <X className="h-4 w-4" />
          </button>
        </div>
      </div>
    </div>
  )
}

export default UpdateNotification
