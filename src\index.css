@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  font-family: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, sans-serif;
  line-height: 1.6;
  font-weight: 400;

  /* Modern Color Palette - Gold Theme */
  --primary-50: #fffbeb;
  --primary-100: #fef3c7;
  --primary-500: #f59e0b;
  --primary-600: #d97706;
  --primary-700: #b45309;
  --primary-800: #92400e;

  /* Gradients */
  --gradient-primary: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  --gradient-secondary: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);

  /* Shadows */
  --shadow-soft: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-medium: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-large: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

body {
  margin: 0;
  min-width: 320px;
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

#root {
  width: 100%;
  min-height: 100vh;
}

/* Custom Components */
@layer components {
  .btn-primary {
    @apply inline-flex items-center px-4 py-2 bg-gradient-to-r from-amber-600 to-yellow-500 text-white font-medium rounded-lg shadow-md hover:from-amber-700 hover:to-yellow-600 focus:outline-none focus:ring-2 focus:ring-amber-500 focus:ring-offset-2 transition-all duration-200;
  }

  .btn-secondary {
    @apply inline-flex items-center px-4 py-2 bg-white text-gray-700 font-medium rounded-lg border border-gray-300 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:ring-offset-2 transition-all duration-200;
  }

  .card {
    @apply bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden;
  }

  .card-header {
    @apply px-6 py-4 border-b border-gray-100 bg-gradient-to-r from-gray-50 to-white;
  }

  .input-field {
    @apply block w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-amber-500 transition-colors duration-200;
  }

  .mobile-card {
    @apply block md:hidden bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-4;
  }
}
