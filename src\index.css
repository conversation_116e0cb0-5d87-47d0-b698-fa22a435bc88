@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  font-family: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, sans-serif;
  line-height: 1.6;
  font-weight: 400;

  /* Modern Color Palette */
  --primary-50: #ecfdf5;
  --primary-100: #d1fae5;
  --primary-500: #10b981;
  --primary-600: #059669;
  --primary-700: #047857;
  --primary-800: #065f46;

  /* Gradients */
  --gradient-primary: linear-gradient(135deg, #10b981 0%, #059669 100%);
  --gradient-secondary: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);

  /* Shadows */
  --shadow-soft: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-medium: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-large: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

body {
  margin: 0;
  min-width: 320px;
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

#root {
  width: 100%;
  min-height: 100vh;
}

/* Custom Components */
@layer components {
  .btn-primary {
    @apply inline-flex items-center px-4 py-2 bg-gradient-to-r from-emerald-600 to-emerald-500 text-white font-medium rounded-lg shadow-md hover:from-emerald-700 hover:to-emerald-600 focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:ring-offset-2 transition-all duration-200;
  }

  .btn-secondary {
    @apply inline-flex items-center px-4 py-2 bg-white text-gray-700 font-medium rounded-lg border border-gray-300 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:ring-offset-2 transition-all duration-200;
  }

  .card {
    @apply bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden;
  }

  .card-header {
    @apply px-6 py-4 border-b border-gray-100 bg-gradient-to-r from-gray-50 to-white;
  }

  .input-field {
    @apply block w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-colors duration-200;
  }

  .mobile-card {
    @apply block md:hidden bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-4;
  }
}
