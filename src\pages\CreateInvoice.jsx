import React, { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { Plus, Trash2, Save } from 'lucide-react'
import { getProducts, getCustomers, saveInvoice } from '../services/storage'

const CreateInvoice = () => {
  const navigate = useNavigate()
  const [products, setProducts] = useState([])
  const [customers, setCustomers] = useState([])
  const [formData, setFormData] = useState({
    pelangganId: '',
    tanggal: new Date().toISOString().split('T')[0],
    items: [{ produkId: '', quantity: 1, harga: 0 }],
    status: 'draft'
  })

  useEffect(() => {
    setProducts(getProducts())
    setCustomers(getCustomers())
  }, [])

  const handleCustomerChange = (customerId) => {
    setFormData({ ...formData, pelangganId: customerId })
  }

  const handleItemChange = (index, field, value) => {
    const newItems = [...formData.items]
    newItems[index][field] = value
    
    // Auto-fill price when product is selected
    if (field === 'produkId') {
      const product = products.find(p => p.id === value)
      if (product) {
        newItems[index].harga = product.harga
      }
    }
    
    setFormData({ ...formData, items: newItems })
  }

  const addItem = () => {
    setFormData({
      ...formData,
      items: [...formData.items, { produkId: '', quantity: 1, harga: 0 }]
    })
  }

  const removeItem = (index) => {
    if (formData.items.length > 1) {
      const newItems = formData.items.filter((_, i) => i !== index)
      setFormData({ ...formData, items: newItems })
    }
  }

  const calculateTotal = () => {
    return formData.items.reduce((total, item) => {
      return total + (item.quantity * item.harga)
    }, 0)
  }

  const handleSubmit = (e) => {
    e.preventDefault()
    
    // Validate form
    if (!formData.pelangganId) {
      alert('Pilih pelanggan terlebih dahulu')
      return
    }
    
    if (formData.items.some(item => !item.produkId || item.quantity <= 0)) {
      alert('Pastikan semua item memiliki produk dan quantity yang valid')
      return
    }
    
    // Calculate total
    const total = calculateTotal()
    
    // Save invoice
    const invoiceData = {
      ...formData,
      total
    }
    
    const savedInvoice = saveInvoice(invoiceData)
    
    if (savedInvoice) {
      alert('Invoice berhasil dibuat!')
      navigate('/invoices')
    } else {
      alert('Gagal membuat invoice')
    }
  }

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR'
    }).format(amount)
  }

  const getProductName = (productId) => {
    const product = products.find(p => p.id === productId)
    return product ? product.nama : ''
  }

  return (
    <div className="max-w-5xl mx-auto space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl md:text-3xl font-bold bg-gradient-to-r from-emerald-600 to-emerald-500 bg-clip-text text-transparent">
          Buat Invoice Baru
        </h1>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Invoice Info */}
        <div className="card">
          <div className="card-header">
            <h2 className="text-lg font-semibold text-gray-900">Informasi Invoice</h2>
          </div>
          <div className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Pelanggan</label>
                <select
                  required
                  value={formData.pelangganId}
                  onChange={(e) => handleCustomerChange(e.target.value)}
                  className="input-field"
                >
                  <option value="">Pilih Pelanggan</option>
                  {customers.map(customer => (
                    <option key={customer.id} value={customer.id}>
                      {customer.nama}
                    </option>
                  ))}
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Tanggal</label>
                <input
                  type="date"
                  required
                  value={formData.tanggal}
                  onChange={(e) => setFormData({ ...formData, tanggal: e.target.value })}
                  className="input-field"
                />
              </div>
            </div>
          </div>
        </div>

        {/* Items */}
        <div className="card">
          <div className="card-header">
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
              <h2 className="text-lg font-semibold text-gray-900">Item Invoice</h2>
              <button
                type="button"
                onClick={addItem}
                className="btn-primary text-sm w-full sm:w-auto"
              >
                <Plus className="h-4 w-4 mr-2" />
                Tambah Item
              </button>
            </div>
          </div>

          <div className="p-6">
            <div className="space-y-4">
              {formData.items.map((item, index) => (
                <div key={index} className="bg-gray-50 rounded-xl p-4 border border-gray-200">
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div className="md:col-span-2">
                      <label className="block text-sm font-medium text-gray-700 mb-2">Produk</label>
                      <select
                        required
                        value={item.produkId}
                        onChange={(e) => handleItemChange(index, 'produkId', e.target.value)}
                        className="input-field"
                      >
                        <option value="">Pilih Produk</option>
                        {products.map(product => (
                          <option key={product.id} value={product.id}>
                            {product.nama} - {formatCurrency(product.harga)}
                          </option>
                        ))}
                      </select>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Quantity</label>
                      <input
                        type="number"
                        required
                        min="1"
                        value={item.quantity}
                        onChange={(e) => handleItemChange(index, 'quantity', parseInt(e.target.value))}
                        className="input-field"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Harga</label>
                      <input
                        type="number"
                        required
                        min="0"
                        step="0.01"
                        value={item.harga}
                        onChange={(e) => handleItemChange(index, 'harga', parseFloat(e.target.value))}
                        className="input-field"
                      />
                    </div>
                  </div>
                  <div className="flex justify-between items-center mt-4 pt-4 border-t border-gray-200">
                    <div className="text-sm text-gray-600">
                      Subtotal: <span className="font-semibold text-emerald-600">{formatCurrency(item.quantity * item.harga)}</span>
                    </div>
                    <button
                      type="button"
                      onClick={() => removeItem(index)}
                      disabled={formData.items.length === 1}
                      className="px-3 py-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      <Trash2 className="h-4 w-4" />
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Total */}
          <div className="mt-6 pt-6 border-t border-gray-200 bg-gradient-to-r from-emerald-50 to-green-50 rounded-lg p-4">
            <div className="flex justify-between items-center">
              <div className="text-sm text-gray-600">
                <p>Total {formData.items.length} item(s)</p>
              </div>
              <div className="text-right">
                <p className="text-sm text-gray-600 mb-1">Total Pembayaran</p>
                <p className="text-2xl font-bold bg-gradient-to-r from-emerald-600 to-emerald-500 bg-clip-text text-transparent">
                  {formatCurrency(calculateTotal())}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Actions */}
        <div className="flex flex-col sm:flex-row justify-end gap-4">
          <button
            type="button"
            onClick={() => navigate('/invoices')}
            className="btn-secondary w-full sm:w-auto"
          >
            Batal
          </button>
          <button
            type="submit"
            className="btn-primary w-full sm:w-auto"
          >
            <Save className="h-4 w-4 mr-2" />
            Simpan Invoice
          </button>
        </div>
      </form>
    </div>
  )
}

export default CreateInvoice
