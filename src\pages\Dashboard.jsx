import React, { useState, useEffect } from 'react'
import { Link } from 'react-router-dom'
import { Package, Users, FileText, DollarSign, TrendingUp } from 'lucide-react'
import { getProducts, getCustomers, getInvoices, initializeSampleData, resetToDefaultData } from '../services/storage'

const Dashboard = () => {
  const [stats, setStats] = useState({
    totalProducts: 0,
    totalCustomers: 0,
    totalInvoices: 0,
    totalRevenue: 0,
    recentInvoices: []
  })

  useEffect(() => {
    // Initialize sample data if needed
    initializeSampleData()

    // Load data
    const products = getProducts()
    const customers = getCustomers()
    const invoices = getInvoices()

    // Calculate stats
    const totalRevenue = invoices.reduce((sum, invoice) => sum + (invoice.total || 0), 0)
    const recentInvoices = invoices
      .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
      .slice(0, 5)
      .map(invoice => {
        const customer = customers.find(c => c.id === invoice.pelangganId)
        return {
          ...invoice,
          customerName: customer ? customer.nama : 'Pelanggan Tidak Ditemukan'
        }
      })

    setStats({
      totalProducts: products.length,
      totalCustomers: customers.length,
      totalInvoices: invoices.length,
      totalRevenue,
      recentInvoices
    })
  }, [])

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR'
    }).format(amount)
  }

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('id-ID')
  }

  const getStatusColor = (status) => {
    switch (status) {
      case 'paid':
        return 'bg-green-100 text-green-800'
      case 'sent':
        return 'bg-blue-100 text-blue-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const handleResetData = () => {
    if (window.confirm('Apakah Anda yakin ingin mereset semua data ke data default? Semua data yang ada akan hilang.')) {
      resetToDefaultData()
      window.location.reload() // Refresh page to show new data
    }
  }

  const statCards = [
    {
      title: 'Total Produk',
      value: stats.totalProducts,
      icon: Package,
      color: 'bg-blue-500',
      link: '/products'
    },
    {
      title: 'Total Pelanggan',
      value: stats.totalCustomers,
      icon: Users,
      color: 'bg-green-500',
      link: '/customers'
    },
    {
      title: 'Total Invoice',
      value: stats.totalInvoices,
      icon: FileText,
      color: 'bg-purple-500',
      link: '/invoices'
    },
    {
      title: 'Total Pendapatan',
      value: formatCurrency(stats.totalRevenue),
      icon: DollarSign,
      color: 'bg-yellow-500',
      link: '/invoices'
    }
  ]

  return (
    <div className="space-y-6">
      {/* Welcome Section */}
      <div className="bg-white rounded-lg shadow p-6">
        <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
          <div className="flex-1">
            <h1 className="text-2xl font-bold text-gray-900">Selamat Datang di Roti Ragil</h1>
            <p className="text-gray-600 mt-1">Kelola invoice dan bisnis Anda dengan mudah</p>
          </div>
          <div className="flex items-center space-x-4">
            <button
              onClick={handleResetData}
              className="inline-flex items-center px-3 py-2 border border-red-300 text-sm font-medium rounded-lg text-red-700 bg-red-50 hover:bg-red-100 hover:border-red-400 transition-colors duration-200 shadow-sm"
            >
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
              Reset Data
            </button>
            <div className="hidden sm:block">
              <TrendingUp className="h-12 w-12 text-blue-500" />
            </div>
          </div>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {statCards.map((card, index) => {
          const Icon = card.icon
          return (
            <Link
              key={index}
              to={card.link}
              className="bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow"
            >
              <div className="flex items-center">
                <div className={`${card.color} rounded-lg p-3`}>
                  <Icon className="h-6 w-6 text-white" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">{card.title}</p>
                  <p className="text-2xl font-semibold text-gray-900">{card.value}</p>
                </div>
              </div>
            </Link>
          )
        })}
      </div>

      {/* Recent Invoices */}
      <div className="bg-white rounded-lg shadow">
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <h2 className="text-lg font-medium text-gray-900">Invoice Terbaru</h2>
            <Link
              to="/invoices"
              className="text-sm text-blue-600 hover:text-blue-500"
            >
              Lihat Semua
            </Link>
          </div>
        </div>
        <div className="divide-y divide-gray-200">
          {stats.recentInvoices.length > 0 ? (
            stats.recentInvoices.map((invoice) => (
              <div key={invoice.id} className="px-6 py-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-900">
                      {invoice.nomorInvoice}
                    </p>
                    <p className="text-sm text-gray-600">
                      {invoice.customerName}
                    </p>
                    <p className="text-sm text-gray-500">
                      {formatDate(invoice.createdAt)}
                    </p>
                  </div>
                  <div className="flex items-center space-x-4">
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(invoice.status)}`}>
                      {invoice.status === 'paid' ? 'Lunas' :
                       invoice.status === 'sent' ? 'Terkirim' : 'Draft'}
                    </span>
                    <p className="text-sm font-medium text-gray-900">
                      {formatCurrency(invoice.total || 0)}
                    </p>
                  </div>
                </div>
              </div>
            ))
          ) : (
            <div className="px-6 py-8 text-center">
              <FileText className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">Belum ada invoice</h3>
              <p className="mt-1 text-sm text-gray-500">
                Mulai dengan membuat invoice pertama Anda.
              </p>
              <div className="mt-6">
                <Link
                  to="/invoices/create"
                  className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
                >
                  Buat Invoice
                </Link>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default Dashboard
