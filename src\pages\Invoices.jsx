import React, { useState, useEffect } from 'react'
import { Link } from 'react-router-dom'
import { FileText, Eye, Download, Edit, Image } from 'lucide-react'
import { getInvoices, getCustomers, updateInvoice, getProducts } from '../services/storage'
import { generatePDF } from '../services/pdfService'
import { generateInvoiceImage } from '../services/imageService'

const Invoices = () => {
  const [invoices, setInvoices] = useState([])
  const [customers, setCustomers] = useState([])

  useEffect(() => {
    loadData()
  }, [])

  const loadData = () => {
    const invoiceData = getInvoices()
    const customerData = getCustomers()
    setInvoices(invoiceData)
    setCustomers(customerData)
  }

  const getCustomerName = (customerId) => {
    const customer = customers.find(c => c.id === customerId)
    return customer ? customer.nama : 'Unknown'
  }

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR'
    }).format(amount)
  }

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('id-ID')
  }

  const getStatusColor = (status) => {
    switch (status) {
      case 'paid':
        return 'bg-green-100 text-green-800'
      case 'sent':
        return 'bg-blue-100 text-blue-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusText = (status) => {
    switch (status) {
      case 'paid':
        return 'Lunas'
      case 'sent':
        return 'Terkirim'
      default:
        return 'Draft'
    }
  }

  const handleStatusChange = (invoiceId, newStatus) => {
    updateInvoice(invoiceId, { status: newStatus })
    loadData()
  }

  const handleDownloadPDF = async (invoice) => {
    const customer = customers.find(c => c.id === invoice.pelangganId)
    await generatePDF(invoice, customer)
  }

  const handleDownloadImage = async (invoice) => {
    // Create a temporary invoice detail element for image generation
    const customer = customers.find(c => c.id === invoice.pelangganId)

    // Create temporary element
    const tempDiv = document.createElement('div')
    tempDiv.id = 'temp-invoice-content'
    tempDiv.style.position = 'absolute'
    tempDiv.style.left = '-9999px'
    tempDiv.style.width = '800px'
    tempDiv.style.backgroundColor = 'white'
    tempDiv.style.padding = '20px'
    tempDiv.style.fontFamily = 'Inter, system-ui, sans-serif'

    // Create invoice HTML content
    tempDiv.innerHTML = `
      <div style="border: 1px solid #e5e7eb; border-radius: 12px; overflow: hidden; background: white;">
        <!-- Header -->
        <div style="padding: 24px; border-bottom: 1px solid #f3f4f6; background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);">
          <div style="display: flex; justify-content: space-between; align-items: start;">
            <div>
              <h1 style="font-size: 28px; font-weight: bold; color: #059669; margin: 0 0 8px 0;">ROTI RAGIL</h1>
              <p style="font-size: 16px; font-weight: 600; color: #1f2937; background: #fef3c7; padding: 4px 12px; border-radius: 6px; border: 1px solid #f59e0b; margin: 4px 0;">No. P-IRT: 2053471011676-30</p>
              <p style="font-size: 15px; font-weight: 500; color: #374151; margin: 0;">Telp: 0895402652626</p>
            </div>
            <div style="text-align: right;">
              <h2 style="font-size: 24px; font-weight: bold; color: #374151; margin: 0 0 8px 0;">INVOICE</h2>
              <p style="font-size: 14px; color: #6b7280; margin: 0;">No: ${invoice.nomorInvoice}</p>
              <p style="font-size: 14px; color: #6b7280; margin: 0;">Tanggal: ${formatDate(invoice.tanggal)}</p>
            </div>
          </div>
        </div>

        <!-- Customer -->
        <div style="padding: 24px; border-bottom: 1px solid #f3f4f6;">
          <h3 style="font-size: 18px; font-weight: 600; color: #374151; margin: 0 0 12px 0;">Kepada:</h3>
          <div style="background: #f9fafb; padding: 16px; border-radius: 8px;">
            <p style="font-size: 18px; font-weight: 600; color: #374151; margin: 0;">${customer ? customer.nama : 'Unknown'}</p>
          </div>
        </div>

        <!-- Items -->
        <div style="padding: 24px;">
          <table style="width: 100%; border-collapse: collapse;">
            <thead>
              <tr style="background: linear-gradient(135deg, #f9fafb 0%, #f3f4f6 100%);">
                <th style="padding: 12px; text-align: left; font-size: 12px; font-weight: 500; color: #6b7280; text-transform: uppercase;">No</th>
                <th style="padding: 12px; text-align: left; font-size: 12px; font-weight: 500; color: #6b7280; text-transform: uppercase;">Produk</th>
                <th style="padding: 12px; text-align: center; font-size: 12px; font-weight: 500; color: #6b7280; text-transform: uppercase;">Qty</th>
                <th style="padding: 12px; text-align: right; font-size: 12px; font-weight: 500; color: #6b7280; text-transform: uppercase;">Harga</th>
                <th style="padding: 12px; text-align: right; font-size: 12px; font-weight: 500; color: #6b7280; text-transform: uppercase;">Total</th>
              </tr>
            </thead>
            <tbody>
              ${invoice.items.map((item, index) => {
                const productsData = getProducts()
                const product = productsData.find(p => p.id === item.produkId)
                const productName = product ? product.nama : 'Produk Tidak Ditemukan'
                return `
                  <tr style="border-bottom: 1px solid #f3f4f6;">
                    <td style="padding: 16px; font-size: 14px; font-weight: 500; color: #374151;">${index + 1}</td>
                    <td style="padding: 16px; font-size: 14px; font-weight: 500; color: #374151;">${productName}</td>
                    <td style="padding: 16px; font-size: 14px; font-weight: 500; color: #374151; text-align: center;">${item.quantity}</td>
                    <td style="padding: 16px; font-size: 14px; color: #374151; text-align: right;">${formatCurrency(item.harga)}</td>
                    <td style="padding: 16px; font-size: 14px; font-weight: 600; color: #374151; text-align: right;">${formatCurrency(item.quantity * item.harga)}</td>
                  </tr>
                `
              }).join('')}
            </tbody>
          </table>
        </div>

        <!-- Total -->
        <div style="padding: 24px; border-top: 1px solid #f3f4f6; background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%);">
          <div style="display: flex; justify-content: space-between; align-items: center;">
            <div>
              <p style="font-size: 14px; font-weight: 500; color: #374151; margin: 0;">Terima kasih atas kepercayaan Anda!</p>
              <p style="font-size: 14px; color: #6b7280; margin: 0;">Pembayaran dapat dilakukan melalui transfer bank atau tunai.</p>
            </div>
            <div style="text-align: right;">
              <p style="font-size: 14px; color: #6b7280; margin: 0 0 4px 0;">Total Pembayaran</p>
              <p style="font-size: 24px; font-weight: bold; color: #059669; margin: 0;">${formatCurrency(invoice.total)}</p>
            </div>
          </div>
        </div>
      </div>
    `

    document.body.appendChild(tempDiv)

    try {
      await generateInvoiceImage(invoice, customer, 'temp-invoice-content')
    } finally {
      document.body.removeChild(tempDiv)
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <h1 className="text-2xl md:text-3xl font-bold bg-gradient-to-r from-emerald-600 to-emerald-500 bg-clip-text text-transparent">
          Daftar Invoice
        </h1>
        <Link
          to="/invoices/create"
          className="btn-primary text-sm w-full sm:w-auto"
        >
          <FileText className="h-4 w-4 mr-2" />
          Buat Invoice
        </Link>
      </div>

      {/* Invoices List */}
      <div className="card">
        {invoices.length > 0 ? (
          <>
            {/* Desktop Table */}
            <div className="hidden md:block overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gradient-to-r from-gray-50 to-gray-100">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      No. Invoice
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Pelanggan
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Tanggal
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Total
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Aksi
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {invoices.map((invoice) => (
                    <tr key={invoice.id} className="hover:bg-gray-50 transition-colors">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">
                          {invoice.nomorInvoice}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">
                          {getCustomerName(invoice.pelangganId)}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">
                          {formatDate(invoice.tanggal)}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-semibold text-emerald-600">
                          {formatCurrency(invoice.total)}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <select
                          value={invoice.status}
                          onChange={(e) => handleStatusChange(invoice.id, e.target.value)}
                          className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border-0 ${getStatusColor(invoice.status)}`}
                        >
                          <option value="draft">Draft</option>
                          <option value="sent">Terkirim</option>
                          <option value="paid">Lunas</option>
                        </select>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <div className="flex justify-end space-x-2">
                          <Link
                            to={`/invoices/${invoice.id}`}
                            className="text-emerald-600 hover:text-emerald-900 p-1 rounded hover:bg-emerald-50 transition-colors"
                            title="Lihat Detail"
                          >
                            <Eye className="h-4 w-4" />
                          </Link>
                          <button
                            onClick={() => handleDownloadImage(invoice)}
                            className="text-blue-600 hover:text-blue-900 p-1 rounded hover:bg-blue-50 transition-colors"
                            title="Download Gambar"
                          >
                            <Image className="h-4 w-4" />
                          </button>
                          <button
                            onClick={() => handleDownloadPDF(invoice)}
                            className="text-red-600 hover:text-red-900 p-1 rounded hover:bg-red-50 transition-colors"
                            title="Download PDF"
                          >
                            <Download className="h-4 w-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {/* Mobile Cards */}
            <div className="md:hidden p-4 space-y-4">
              {invoices.map((invoice) => (
                <div key={invoice.id} className="bg-gray-50 rounded-lg p-4 border border-gray-200">
                  <div className="flex justify-between items-start mb-3">
                    <div className="flex-1">
                      <h3 className="font-semibold text-gray-900">{invoice.nomorInvoice}</h3>
                      <p className="text-sm text-gray-600">{getCustomerName(invoice.pelangganId)}</p>
                      <p className="text-lg font-bold text-emerald-600 mt-1">{formatCurrency(invoice.total)}</p>
                    </div>
                    <div className="flex flex-col items-end gap-2">
                      <select
                        value={invoice.status}
                        onChange={(e) => handleStatusChange(invoice.id, e.target.value)}
                        className={`px-2.5 py-0.5 rounded-full text-xs font-medium border-0 ${getStatusColor(invoice.status)}`}
                      >
                        <option value="draft">Draft</option>
                        <option value="sent">Terkirim</option>
                        <option value="paid">Lunas</option>
                      </select>
                    </div>
                  </div>
                  <div className="flex justify-between items-center">
                    <div className="text-sm text-gray-600">
                      {formatDate(invoice.tanggal)}
                    </div>
                    <div className="flex space-x-2">
                      <Link
                        to={`/invoices/${invoice.id}`}
                        className="text-emerald-600 hover:text-emerald-900 p-2 rounded-lg hover:bg-emerald-50 transition-colors"
                        title="Lihat Detail"
                      >
                        <Eye className="h-4 w-4" />
                      </Link>
                      <button
                        onClick={() => handleDownloadImage(invoice)}
                        className="text-blue-600 hover:text-blue-900 p-2 rounded-lg hover:bg-blue-50 transition-colors"
                        title="Download Gambar"
                      >
                        <Image className="h-4 w-4" />
                      </button>
                      <button
                        onClick={() => handleDownloadPDF(invoice)}
                        className="text-red-600 hover:text-red-900 p-2 rounded-lg hover:bg-red-50 transition-colors"
                        title="Download PDF"
                      >
                        <Download className="h-4 w-4" />
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </>
        ) : (
          <div className="text-center py-12">
            <FileText className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">Belum ada invoice</h3>
            <p className="mt-1 text-sm text-gray-500">
              Mulai dengan membuat invoice pertama Anda.
            </p>
            <div className="mt-6">
              <Link
                to="/invoices/create"
                className="btn-primary"
              >
                <FileText className="h-4 w-4 mr-2" />
                Buat Invoice
              </Link>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export default Invoices
