import html2canvas from 'html2canvas'

/**
 * Generate and download invoice as PNG image
 * @param {Object} invoice - Invoice data
 * @param {Object} customer - Customer data
 * @param {string} elementId - ID of the element to capture (default: 'invoice-content')
 */
export const generateInvoiceImage = async (invoice, customer, elementId = 'invoice-content') => {
  try {
    // Find the element to capture
    const element = document.getElementById(elementId)
    if (!element) {
      throw new Error('Invoice content element not found')
    }

    // Show loading state
    const loadingToast = showLoadingToast()

    // Configure html2canvas options for better quality
    const canvas = await html2canvas(element, {
      scale: 2, // Higher resolution
      useCORS: true,
      allowTaint: true,
      backgroundColor: '#ffffff',
      width: element.offsetWidth,
      height: element.offsetHeight,
      scrollX: 0,
      scrollY: 0,
      logging: false,
      onclone: (clonedDoc) => {
        // Hide export buttons and other UI elements in the cloned document
        const exportButtons = clonedDoc.querySelectorAll('.export-buttons, .no-print')
        exportButtons.forEach(btn => {
          btn.style.display = 'none'
        })

        // Ensure proper styling in cloned document
        const clonedElement = clonedDoc.getElementById(elementId)
        if (clonedElement) {
          clonedElement.style.transform = 'none'
          clonedElement.style.maxWidth = 'none'
          clonedElement.style.margin = '0'
          clonedElement.style.padding = '20px'
          clonedElement.style.boxShadow = 'none'
          clonedElement.style.border = '1px solid #e5e7eb'
        }

        // Ensure gradients are visible
        const gradientElements = clonedDoc.querySelectorAll('[class*="gradient"], [class*="bg-gradient"]')
        gradientElements.forEach(el => {
          if (el.style.backgroundImage.includes('gradient')) {
            // Keep gradient as is
          } else if (el.classList.contains('bg-clip-text')) {
            // For text gradients, use fallback color
            el.style.color = '#059669'
            el.style.backgroundImage = 'none'
            el.style.webkitBackgroundClip = 'unset'
            el.style.backgroundClip = 'unset'
          }
        })

        // Ensure all text is visible
        const allElements = clonedDoc.querySelectorAll('*')
        allElements.forEach(el => {
          const computedStyle = window.getComputedStyle(el)
          if (computedStyle.color === 'transparent' || computedStyle.color === 'rgba(0, 0, 0, 0)') {
            el.style.color = '#374151'
          }
        })
      }
    })

    // Convert canvas to blob
    canvas.toBlob((blob) => {
      if (blob) {
        // Create download link
        const url = URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url
        link.download = `Invoice-${invoice.nomorInvoice || 'Unknown'}-${new Date().toISOString().split('T')[0]}.png`
        
        // Trigger download
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        
        // Clean up
        URL.revokeObjectURL(url)
        
        // Hide loading and show success
        hideLoadingToast(loadingToast)
        showSuccessToast('Invoice berhasil diexport sebagai gambar!')
      } else {
        throw new Error('Failed to generate image')
      }
    }, 'image/png', 0.95)

  } catch (error) {
    console.error('Error generating invoice image:', error)
    showErrorToast('Gagal mengexport invoice sebagai gambar. Silakan coba lagi.')
    throw error
  }
}

/**
 * Generate invoice image for preview (returns base64 data URL)
 * @param {string} elementId - ID of the element to capture
 * @returns {Promise<string>} Base64 data URL of the image
 */
export const generateInvoiceImagePreview = async (elementId = 'invoice-content') => {
  try {
    const element = document.getElementById(elementId)
    if (!element) {
      throw new Error('Invoice content element not found')
    }

    const canvas = await html2canvas(element, {
      scale: 1,
      useCORS: true,
      allowTaint: true,
      backgroundColor: '#ffffff',
      onclone: (clonedDoc) => {
        // Hide export buttons in preview
        const exportButtons = clonedDoc.querySelectorAll('.export-buttons, .no-print')
        exportButtons.forEach(btn => {
          btn.style.display = 'none'
        })
      }
    })

    return canvas.toDataURL('image/png', 0.8)
  } catch (error) {
    console.error('Error generating invoice preview:', error)
    throw error
  }
}

// Toast notification helpers
const showLoadingToast = () => {
  const toast = document.createElement('div')
  toast.id = 'loading-toast'
  toast.className = 'fixed top-4 right-4 bg-blue-500 text-white px-4 py-2 rounded-lg shadow-lg z-50 flex items-center'
  toast.innerHTML = `
    <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
      <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
      <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
    </svg>
    Mengexport gambar...
  `
  document.body.appendChild(toast)
  return toast
}

const hideLoadingToast = (toast) => {
  if (toast && toast.parentNode) {
    toast.parentNode.removeChild(toast)
  }
}

const showSuccessToast = (message) => {
  const toast = document.createElement('div')
  toast.className = 'fixed top-4 right-4 bg-green-500 text-white px-4 py-2 rounded-lg shadow-lg z-50'
  toast.textContent = message
  document.body.appendChild(toast)
  
  setTimeout(() => {
    if (toast.parentNode) {
      toast.parentNode.removeChild(toast)
    }
  }, 3000)
}

const showErrorToast = (message) => {
  const toast = document.createElement('div')
  toast.className = 'fixed top-4 right-4 bg-red-500 text-white px-4 py-2 rounded-lg shadow-lg z-50'
  toast.textContent = message
  document.body.appendChild(toast)
  
  setTimeout(() => {
    if (toast.parentNode) {
      toast.parentNode.removeChild(toast)
    }
  }, 5000)
}
