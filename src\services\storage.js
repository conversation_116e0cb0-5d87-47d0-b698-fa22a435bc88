// Storage service untuk mengelola data di localStorage

const STORAGE_KEYS = {
  PRODUCTS: 'roti_ragil_products',
  CUSTOMERS: 'roti_ragil_customers',
  INVOICES: 'roti_ragil_invoices',
  SETTINGS: 'roti_ragil_settings'
}

// Generic storage functions
const getFromStorage = (key) => {
  try {
    const data = localStorage.getItem(key)
    return data ? JSON.parse(data) : []
  } catch (error) {
    console.error('Error reading from storage:', error)
    return []
  }
}

const saveToStorage = (key, data) => {
  try {
    localStorage.setItem(key, JSON.stringify(data))
    return true
  } catch (error) {
    console.error('Error saving to storage:', error)
    return false
  }
}

// Product functions
export const getProducts = () => {
  return getFromStorage(STORAGE_KEYS.PRODUCTS)
}

export const saveProduct = (product) => {
  const products = getProducts()
  const newProduct = {
    id: Date.now().toString(),
    ...product,
    createdAt: new Date().toISOString()
  }
  products.push(newProduct)
  saveToStorage(STORAGE_KEYS.PRODUCTS, products)
  return newProduct
}

export const updateProduct = (id, updatedProduct) => {
  const products = getProducts()
  const index = products.findIndex(p => p.id === id)
  if (index !== -1) {
    products[index] = { ...products[index], ...updatedProduct }
    saveToStorage(STORAGE_KEYS.PRODUCTS, products)
    return products[index]
  }
  return null
}

export const deleteProduct = (id) => {
  const products = getProducts()
  const filteredProducts = products.filter(p => p.id !== id)
  saveToStorage(STORAGE_KEYS.PRODUCTS, filteredProducts)
  return true
}

// Customer functions
export const getCustomers = () => {
  return getFromStorage(STORAGE_KEYS.CUSTOMERS)
}

export const saveCustomer = (customer) => {
  const customers = getCustomers()
  const newCustomer = {
    id: Date.now().toString(),
    ...customer,
    createdAt: new Date().toISOString()
  }
  customers.push(newCustomer)
  saveToStorage(STORAGE_KEYS.CUSTOMERS, customers)
  return newCustomer
}

export const updateCustomer = (id, updatedCustomer) => {
  const customers = getCustomers()
  const index = customers.findIndex(c => c.id === id)
  if (index !== -1) {
    customers[index] = { ...customers[index], ...updatedCustomer }
    saveToStorage(STORAGE_KEYS.CUSTOMERS, customers)
    return customers[index]
  }
  return null
}

export const deleteCustomer = (id) => {
  const customers = getCustomers()
  const filteredCustomers = customers.filter(c => c.id !== id)
  saveToStorage(STORAGE_KEYS.CUSTOMERS, filteredCustomers)
  return true
}

// Invoice functions
export const getInvoices = () => {
  return getFromStorage(STORAGE_KEYS.INVOICES)
}

export const saveInvoice = (invoice) => {
  const invoices = getInvoices()
  const newInvoice = {
    id: Date.now().toString(),
    nomorInvoice: generateInvoiceNumber(),
    ...invoice,
    createdAt: new Date().toISOString()
  }
  invoices.push(newInvoice)
  saveToStorage(STORAGE_KEYS.INVOICES, invoices)
  return newInvoice
}

export const updateInvoice = (id, updatedInvoice) => {
  const invoices = getInvoices()
  const index = invoices.findIndex(i => i.id === id)
  if (index !== -1) {
    invoices[index] = { ...invoices[index], ...updatedInvoice }
    saveToStorage(STORAGE_KEYS.INVOICES, invoices)
    return invoices[index]
  }
  return null
}

export const deleteInvoice = (id) => {
  const invoices = getInvoices()
  const filteredInvoices = invoices.filter(i => i.id !== id)
  saveToStorage(STORAGE_KEYS.INVOICES, filteredInvoices)
  return true
}

export const getInvoiceById = (id) => {
  const invoices = getInvoices()
  return invoices.find(i => i.id === id)
}

// Helper functions
const generateInvoiceNumber = () => {
  const now = new Date()
  const year = now.getFullYear()
  const month = String(now.getMonth() + 1).padStart(2, '0')
  const invoices = getInvoices()
  const invoiceCount = invoices.length + 1
  return `INV/${year}${month}/${String(invoiceCount).padStart(4, '0')}`
}

// Initialize with sample data if empty
export const initializeSampleData = () => {
  if (getProducts().length === 0) {
    const sampleProducts = [
      { nama: 'Roti Sosis', harga: 3000, deskripsi: '' },
      { nama: 'Roti Sosis', harga: 2500, deskripsi: '' },
      { nama: 'Roti Meses', harga: 2500, deskripsi: '' },
      { nama: 'Roti Abon', harga: 2500, deskripsi: '' },
      { nama: 'Roti Keju', harga: 2500, deskripsi: '' },
      { nama: 'Roti Kacang Hijau', harga: 2500, deskripsi: '' },
      { nama: 'Roti Kacang Merah', harga: 2500, deskripsi: '' },
      { nama: 'Flossroll', harga: 3500, deskripsi: '' },
      { nama: 'Bolu Gulung', harga: 35000, deskripsi: '' },
      { nama: 'Bolu Gulung', harga: 30000, deskripsi: '' },
      { nama: 'Chiffon Keju', harga: 35000, deskripsi: '' },
      { nama: 'Chiffon Keju', harga: 30000, deskripsi: '' },
      { nama: 'Flossroll Box', harga: 30000, deskripsi: '' },
      { nama: 'Roti Pisang Cokelat', harga: 3000, deskripsi: '' },
      { nama: 'Roti Pisang Keju', harga: 3000, deskripsi: '' }
    ]
    sampleProducts.forEach(product => saveProduct(product))
  }

  if (getCustomers().length === 0) {
    const sampleCustomers = [
      { nama: 'Bu Lisferi', alamat: '-', telepon: '-', email: '' },
      { nama: 'Dapur Lezzati', alamat: '-', telepon: '-', email: '' },
      { nama: 'Maruf', alamat: '-', telepon: '-', email: '' }
    ]
    sampleCustomers.forEach(customer => saveCustomer(customer))
  }
}

// Reset all data and reinitialize with default data
export const resetToDefaultData = () => {
  // Clear existing data
  localStorage.removeItem(STORAGE_KEYS.PRODUCTS)
  localStorage.removeItem(STORAGE_KEYS.CUSTOMERS)
  localStorage.removeItem(STORAGE_KEYS.INVOICES)

  // Reinitialize with default data
  initializeSampleData()
}


