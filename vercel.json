{"rewrites": [{"source": "/(.*)", "destination": "/index.html"}], "headers": [{"source": "/assets/(.*)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}, {"source": "/(.*\\.(svg|ico|png|jpg|jpeg))", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000"}]}, {"source": "/manifest.json", "headers": [{"key": "Cache-Control", "value": "public, max-age=86400"}]}, {"source": "/sw.js", "headers": [{"key": "Cache-Control", "value": "no-cache"}]}]}