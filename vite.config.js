import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
  server: {
    port: 3000,
    host: true,
    // Handle SPA routing in development
    historyApiFallback: true
  },
  preview: {
    port: 4173,
    host: true,
    // Handle SPA routing in preview mode
    historyApiFallback: true
  },
  build: {
    // Ensure proper build for SPA
    rollupOptions: {
      output: {
        manualChunks: undefined
      }
    }
  }
})
